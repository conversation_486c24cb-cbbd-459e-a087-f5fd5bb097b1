from binance.um_futures import UMFutures
import json
import yaml
from func_timeout import func_set_timeout

from config import settings

# Constants
TIMEOUT_SECONDS = 5
PRECISION_MULTIPLIER = 100


APIKEY_MDS = settings.binance.mds_account.api_key
APIKEY_ZACH = settings.binance.zach_account.api_key  # zach account key
APISECRET_ZACH = settings.binance.zach_account.api_secret  # zach account secret
zach_um_futures_client = UMFutures(key=APIKEY_ZACH, secret=APISECRET_ZACH)


@func_set_timeout(TIMEOUT_SECONDS)
def positions(account, is_save=False):
    position_msgs = []
    try:
        if account == "zach":
            response = zach_um_futures_client.account(recvWindow=6000)
            if is_save:
                json.dump(response, open(f"{account}_balance.json", "w"))
            for position in response["positions"]:
                if float(position["positionAmt"]) != 0:
                    entryPrice = float(position["entryPrice"])
                    positionAmt = float(position["positionAmt"])
                    unRealizedProfit = float(position["unrealizedProfit"])
                    leverage = int(position["leverage"])
                    roi = (
                        round(
                            unRealizedProfit / (entryPrice * positionAmt / leverage), 4
                        )
                        * 100
                    )
                    if position["positionSide"] == "LONG":
                        position_msgs.append(
                            f"====== {account} ======\n{position['symbol']} 多单\n"
                            f"开仓价格：{position['entryPrice']}\n收益率：{roi:.2f} %\n"
                            f"持仓量：{position['positionAmt']}\n当前盈亏：{unRealizedProfit:.2f}\n"
                            f"杠杆：{position['leverage']}"
                        )
                    elif position["positionSide"] == "SHORT":
                        position_msgs.append(
                            f"====== {account} ======\n{position['symbol']} 空单\n"
                            f"开仓价格：{position['entryPrice']}\n"
                            f"收益率：{-roi:.2f} %\n"
                            f"持仓量：{position['positionAmt']}\n当前盈亏：{unRealizedProfit:.2f}\n"
                            f"杠杆：{position['leverage']}"
                        )

        else:
            with open("mds_account.json", "r") as f:
                data = json.load(f)
            positions = data["positions"]
            with open(file="config.yaml", mode="r", encoding="utf-8") as f:
                res = yaml.safe_load(f)
            asset_proportion = res["zach_asset"] / res["mds_asset"] / 2
            margin_proportion = res["zach_margin"] / res["mds_margin"]
            proportion = int(asset_proportion * margin_proportion * 10000) / 10000.0

            for position in positions:
                if "LONG" in positions[position]:
                    position_msgs.append(
                        f"====== {account} ======\n{position} 多单\n"
                        f"开仓价格：{positions[position]['LONG']['ep']}\n"
                        f"持仓量：{positions[position]['LONG']['pa']} "
                        f"({round(positions[position]['LONG']['pa']*proportion,4)})"
                    )
                if "SHORT" in positions[position]:
                    position_msgs.append(
                        f"====== {account} ======\n{position} 空单\n"
                        f"开仓价格：{positions[position]['SHORT']['ep']}\n"
                        f"持仓量：{positions[position]['SHORT']['pa']} "
                        f"({round(positions[position]['SHORT']['pa']*proportion,4)})"
                    )
        if len(position_msgs) == 0:
            return ["没有仓位"]
        else:
            return position_msgs

    except Exception as e:
        return [f"获取{account}仓位失败: {str(e)}"]


@func_set_timeout(TIMEOUT_SECONDS)
def balances(account, is_save=False):
    try:
        if account == "zach":
            response = zach_um_futures_client.account(recvWindow=6000)
            if "assets" in response:
                for asset in response["assets"]:
                    if asset["asset"] == "USDT":
                        zach_asset = int(
                            (float(asset["walletBalance"]) + float(asset["crossUnPnl"]))
                            * PRECISION_MULTIPLIER
                        ) / float(PRECISION_MULTIPLIER)
                if is_save:
                    json.dump(response, open(f"{account}_balance.json", "w"))
                return int(zach_asset * PRECISION_MULTIPLIER) / float(
                    PRECISION_MULTIPLIER
                )

        else:
            mds_asset = json.load(open("mds_account.json", "r"))["asset"]
            return int(mds_asset * PRECISION_MULTIPLIER) / float(PRECISION_MULTIPLIER)

    except Exception as e:
        return f"获取{account}余额失败: {str(e)}"


if __name__ == "__main__":
    print("=============== position ===============")
    print("\n".join(positions("mds")))
    print("\n".join(positions("zach")))
    print("\n=============== balance ===============")
    mds_val = balances("mds", True)
    print(mds_val if isinstance(mds_val, str) else f"{mds_val:.2f}")
    zach_val = balances("zach", True)
    print(zach_val if isinstance(zach_val, str) else f"{zach_val:.2f}")
