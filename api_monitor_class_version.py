import os

import json
from typing import Any
import logging

from util import Telegram
from config import settings
from threading import Timer, Lock
import time

from binance.websocket.um_futures.websocket_client import UMFuturesWebsocketClient
from Binance_api import Binance

import yaml


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(levelname)s %(filename)s:%(funcName)s:%(lineno)d  %(message)s",  # 定义输出log的格式
    datefmt="%Y-%m-%d %A %H:%M:%S",
    filename="log/api_monitor_class_version.log",
    filemode="a+",
)
logger = logging.getLogger()


# 时间常量（秒）
LISTEN_KEY_RENEW_INTERVAL = 45 * 60  # 45分钟
WEBSOCKET_REFRESH_INTERVAL = 23 * 60 * 60  # 23小时
ACCOUNT_UPDATE_INTERVAL = 30 * 60  # 30分钟
UPDATE_RETRY_INTERVAL = 60  # 1分钟
RESTART_WAIT_TIME = 2  # 2秒

# 环境变量
APIKEY_MDS = settings.binance.mds_account.api_key
APIKEY_ZACH = settings.binance.zach_account.api_key
APIKEY_TEST = settings.binance.test_account.api_key
APISECRET = os.getenv("APISECRET", "")
WSS_BASE_URL = os.getenv("WSS_BASE_URL", "wss://fstream.binance.com")
HTTP_BASE_URL = os.getenv("HTTP_BASE_URL", "https://fapi.binance.com")
bot_token = settings.telegram.monitor_bot


class Monitor:

    def __init__(self) -> None:
        # self.client = UMFutures(APIKEY)
        # self.listen_key = self.client.new_listen_key()["listenKey"]
        self.listen_key = (
            "3JpWLKX0vAy64GoLd6aTlJSerueLTGSTRbx8EuG7kk5g2ylNKaXp3bnnpHEPAJPO"
        )
        logger.info(f"MDS ListenKey is inited: {self.listen_key}")
        self.binance_client = Binance(logger)
        logger.info("Binance client inited.")
        self.telegram = Telegram(bot_token, logger)
        logger.info("MDS monitor telegram inited.")
        self.closeOrdersID = {}
        closeOrdersID_json = json.load(open("closeOrdersID.json", "r"))
        for i in closeOrdersID_json:
            self.closeOrdersID[int(i)] = closeOrdersID_json[i]
        logger.info(f"init closeOrdersID: {self.closeOrdersID}")
        json.dump(self.closeOrdersID, open("closeOrdersID.json", "w"))
        # 最近一次账户/持仓更新事件，传入下单/平仓逻辑做数量对齐
        self.positions_event: dict[str, Any] = {}

        # 添加状态锁
        self.restart_lock = Lock()
        self.is_restarting = False

        Timer(LISTEN_KEY_RENEW_INTERVAL, self.update_listen_key).start()
        self.update_proportion_accountInfo(True)

    def start(self):
        self.ws_client = None
        self.timer = None
        self.create_websocket()

    def create_websocket(self):
        try:
            self.ws_client = UMFuturesWebsocketClient(
                on_message=self.handle_message,
                on_close=self.handle_close,
                on_error=self.handle_error,
            )
            logger.info("create new websocketclient.")
            self.monitor()
        except Exception as e:
            logger.error(e, exc_info=True)
            self.telegram.send_message(e)
            # self.restart()
        finally:
            if self.timer and self.timer.is_alive():
                self.timer.cancel()
                logger.info("cancel old Timer.")
            self.timer = Timer(WEBSOCKET_REFRESH_INTERVAL, self.just_close)
            self.timer.start()
            logger.info("new Timer: update websocket in 23h ...")

    def monitor(self):
        try:
            self.ws_client.user_data(listen_key=self.listen_key)
            msg = "listen in a new websocketclient"
            logger.info(msg)
            self.telegram.send_message(msg)
        except Exception as e:
            logger.error(e, exc_info=True)
            self.restart()
            self.telegram.send_message(e)

    def restart(self):
        # 使用锁避免并发重启
        if not self.restart_lock.acquire(blocking=False):
            logger.info("重启已在进行中，忽略此次请求")
            return

        try:
            # 检查重启状态
            if self.is_restarting:
                logger.info("重启已在进行中，忽略此次请求")
                return

            self.is_restarting = True
            self.telegram.send_message("准备重启websocket")

            # 关闭旧的websocket
            if self.ws_client:
                try:
                    self.ws_client.stop()
                    time.sleep(RESTART_WAIT_TIME)  # 等待充分关闭
                except Exception as e:
                    logger.error(f"close old websocket: {e}")

            self.ws_client = None

            # 创建新的websocket
            try:
                self.create_websocket()
                msg = "restarted websocketclient and listening"
                logger.info(msg)
                self.telegram.send_message(msg)
            except Exception as e:
                logger.error(e, exc_info=True)
                # 使用定时器延迟重试，避免递归调用
                Timer(5, self.restart).start()
        finally:
            self.is_restarting = False
            self.restart_lock.release()

    def just_close(self):
        self.telegram.send_message("websocket即将到期")
        # 只关闭websocket，重启交给handle_close避免重复监听
        if self.ws_client:
            try:
                self.ws_client.stop()
            except Exception as e:
                logger.error(f"close old websocket: {e}")

    def handle_close(self, _):
        msg = "handle close ..."
        logger.info(msg)
        self.telegram.send_message(msg)
        # 检查是否已经在重启中
        if not self.is_restarting:
            # 使用定时器延迟重启
            Timer(1, self.restart).start()
        else:
            logger.info("已在重启过程中，忽略此次关闭事件")

    def handle_error(self, _, error):
        logger.error(error)
        self.telegram.send_message(error)
        # 检查是否已经在重启中
        if not self.is_restarting:
            # 使用定时器延迟重启
            Timer(1, self.restart).start()
        else:
            logger.info("已在重启过程中，忽略此次错误事件")

    def update_listen_key(self):
        try:
            # self.client.renew_listen_key(self.listen_key)
            logger.info("ListenKey is renewed.")
            Timer(LISTEN_KEY_RENEW_INTERVAL, self.update_listen_key).start()
        except Exception as e:
            logger.error(e, exc_info=True)
            Timer(UPDATE_RETRY_INTERVAL, self.update_listen_key).start()

    def new_order_helper(self, event):
        i = event["o"]["i"]  # 订单ID
        symbol = event["o"]["s"]
        side = event["o"]["S"]
        type = event["o"]["o"]  # 订单类型
        amount = float(event["o"]["q"])  # 订单原始数量
        p = float(event["o"]["p"])  # 订单原始价格
        sp = float(event["o"]["sp"])  # 订单平均价格
        positionSide = event["o"]["ps"]  # 持仓方向

        # 只处理平仓挂单
        if (
            side == "BUY"
            and positionSide == "SHORT"
            or side == "SELL"
            and positionSide == "LONG"
        ):
            clientOrderId = self.binance_client.new_close_order(
                symbol, side, type, positionSide, amount, p, sp
            )
            if clientOrderId:
                self.closeOrdersID[i] = clientOrderId
                logger.info(f"Zach挂单 {clientOrderId} -> {i}")

            msg = f"[MDS挂单] 平仓{type}：以 {p} {side} {symbol} {amount} "
        else:
            msg = f"[MDS挂单] 开仓{type}：以 {p} {side} {symbol} {amount} "
        logger.info(msg)
        self.telegram.send_message(msg)

    def cancel_order_helper(self, event):
        i = event["o"]["i"]  # 订单ID
        symbol = event["o"]["s"]
        side = event["o"]["S"]  # 订单方向
        type = event["o"]["o"]  # 订单类型
        z = float(event["o"]["z"])  # 订单累计已成交量
        ap = float(event["o"]["ap"])  # 订单平均价格
        positionSide = event["o"]["ps"]  # 持仓方向

        if z > 0:
            try:
                if side == "BUY":
                    if positionSide == "SHORT":
                        self.binance_client.close_position(
                            symbol, side, positionSide, z, self.positions_event
                        )
                        self.update_proportion_accountInfo()
                        Timer(15, self.update_proportion_accountInfo, (False,)).start()
                        msg = f"[MDS平仓] {symbol} {side} {ap} {positionSide} {z}"
                    elif positionSide == "LONG":
                        self.binance_client.open_position(
                            symbol,
                            side,
                            positionSide,
                            z,
                            ap,
                        )
                        msg = (
                            f"[MDS开仓] {symbol} {side} {type} {positionSide} {z} {ap}"
                        )

                elif side == "SELL":
                    if positionSide == "LONG":
                        self.binance_client.close_position(
                            symbol, side, positionSide, z, self.positions_event
                        )
                        self.update_proportion_accountInfo()
                        Timer(15, self.update_proportion_accountInfo, (False,)).start()
                        msg = f"[MDS平仓] {symbol} {side} {ap} {positionSide} {z}"

                    elif positionSide == "SHORT":
                        self.binance_client.open_position(
                            symbol,
                            side,
                            positionSide,
                            z,
                            ap,
                        )
                        msg = (
                            f"[MDS开仓] {symbol} {side} {type} {positionSide} {z} {ap}"
                        )

                logger.info(msg)
                self.telegram.send_message(msg)
            except Exception as e:
                logger.error(e, exc_info=True)

        if i in self.closeOrdersID:
            self.binance_client.cancel_order(
                symbol, origClientOrderId=self.closeOrdersID.pop(i)
            )
            msg = f"[MDS] 取消平仓挂单(已跟随)： {symbol} {side} {ap} {type} {positionSide} "
            self.telegram.send_message(msg)
        elif (
            side == "BUY"
            and positionSide == "SHORT"
            or side == "SELL"
            and positionSide == "LONG"
        ):
            msg = f"[MDS] 取消平仓挂单(未跟随)： {symbol} {side} {ap} {type} {positionSide} "
            self.telegram.send_message(msg)
        else:
            msg = f"[MDS] 取消开仓挂单： {symbol} {side} {ap} {type} {positionSide} "
            self.telegram.send_message(msg)

    def filled_order_trade_helper(self, event):
        symbol = event["o"]["s"]
        side = event["o"]["S"]  # 订单方向
        type = event["o"]["o"]  # 订单类型
        amount = float(event["o"]["q"])  # 订单原始数量
        ap = float(event["o"]["ap"])  # 订单平均价格
        positionSide = event["o"]["ps"]  # 持仓方向

        try:
            if side == "BUY":
                if positionSide == "SHORT":
                    self.binance_client.close_position(
                        symbol,
                        side,
                        positionSide,
                        amount,
                        self.positions_event,
                        is_close_remain=True,
                    )
                    self.update_proportion_accountInfo()
                    Timer(15, self.update_proportion_accountInfo, (False,)).start()
                    msg = f"[MDS平仓] {symbol} {side} {ap} {positionSide} {amount}"
                elif positionSide == "LONG":
                    self.binance_client.open_position(
                        symbol,
                        side,
                        positionSide,
                        amount,
                        ap,
                    )
                    msg = (
                        f"[MDS开仓] {symbol} {side} {type} {positionSide} {amount} {ap}"
                    )

            elif side == "SELL":
                if positionSide == "LONG":
                    self.binance_client.close_position(
                        symbol,
                        side,
                        positionSide,
                        amount,
                        self.positions_event,
                        is_close_remain=True,
                    )
                    self.update_proportion_accountInfo()
                    Timer(15, self.update_proportion_accountInfo, (False,)).start()
                    msg = f"[MDS平仓] {symbol} {side} {ap} {positionSide} {amount}"

                elif positionSide == "SHORT":
                    self.binance_client.open_position(
                        symbol,
                        side,
                        positionSide,
                        amount,
                        ap,
                    )
                    msg = (
                        f"[MDS开仓] {symbol} {side} {type} {positionSide} {amount} {ap}"
                    )

            logger.info(msg)
            self.telegram.send_message(msg)
        except Exception as e:
            logger.error(e, exc_info=True)

    def account_config_helper(self, event):
        if "ac" in event:
            # 交易对杠杆倍数发生变化
            sym = event["ac"]["s"]
            leverage = event["ac"]["l"]
            self.binance_client.change_leverage(sym, leverage)
            msg = f"[MDS] change {sym} leverage to {leverage}"
            logger.info(msg)
            self.telegram.send_message(msg)
        if "ai" in event:
            # 联合保证金状态发生变化
            event["ai"]["j"]

    def handle_message(self, _, message):
        event = json.loads(message)
        if "e" in event:
            if event["e"] == "ORDER_TRADE_UPDATE":
                # 订单交易更新推送
                if "o" in event:
                    x = event["o"]["x"]  # 本次事件的具体执行类型
                    X = event["o"]["X"]  # 订单的当前状态
                    if X == "FILLED" and x == "TRADE":  # 订单成交才操作
                        self.filled_order_trade_helper(event)
                    elif X == "NEW":
                        self.new_order_helper(event)
                    elif X == "CANCELED":  # cancel
                        self.cancel_order_helper(event)
                    elif X == "PARTIALLY_FILLED":
                        ...
                    elif X == "EXPIRED":
                        ...
                    else:
                        msg = f"[NEW ORDER TYPE] {X}"
                        logger.info(msg)
                        self.telegram.send_message(msg)
            elif event["e"] == "ACCOUNT_CONFIG_UPDATE":
                # 杠杆倍数等账户配置 更新推送
                self.account_config_helper(event)
            elif event["e"] == "ACCOUNT_UPDATE":
                # Balance 和 Position 更新推送
                self.positions_event = event
            elif event["e"] == "listenKeyExpired":
                # listenKey过期推送
                self.listen_key = self.client.new_listen_key()["listenKey"]
                logger.info(f"[listenKeyExpired] new listen key: {self.listen_key}")
        logger.info(event)

    def update_proportion_accountInfo(self, is_timer=False):
        self.binance_client.update_accountInfo(is_timer)

        if len(self.binance_client.positions) == 0:

            self.closeOrdersID = {}

            try:
                with open(file="config.yaml", mode="r", encoding="utf-8") as f:
                    res = yaml.safe_load(f)
                if res is None:
                    logger.warning("Config file loaded but result is None")
            except Exception as e:
                logger.error(f"Error loading config file: {e}")

            self.binance_client.update_proportion()

        if is_timer:
            self.binance_client.update_symbols_info()
            json.dump(self.closeOrdersID, open("closeOrdersID.json", "w"))
            logger.info("update proportion and accountInfo in 30m ...")
            Timer(
                ACCOUNT_UPDATE_INTERVAL, self.update_proportion_accountInfo, (True,)
            ).start()


def main():
    monitor = Monitor()
    monitor.start()


if __name__ == "__main__":
    main()
