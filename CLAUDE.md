# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个币安（Binance）期货跟单交易机器人，支持多账户管理和自动化交易策略。

## 运行说明

### 系统启动（必须同时运行三个进程）

系统采用三进程并行架构，**必须同时启动所有进程**才能正常工作：

```bash
# 终端1: 主监控进程（交易执行）
python3 api_monitor_class_version.py

# 终端2: 辅助监控进程（仓位一致性检查，解决平仓延迟问题）
python3 api_monitor_class_version_log.py

# 终端3: Telegram监控（日志聚合与状态反馈）
python3 Telegram.py
```

### 进程职责说明

1. **api_monitor_class_version.py** → 核心交易引擎
   - 实时监控WebSocket事件
   - 执行开仓/平仓操作
   - 处理主账户→跟随账户的跟单逻辑

2. **api_monitor_class_version_log.py** → 仓位一致性监控
   - 专门处理平仓延迟问题（平仓具有极高时效性）
   - 检测仓位不一致并自动纠正
   - 独立进程确保不影响主交易流程

3. **Telegram.py** → 运维监控中心
   - 读取并解析所有日志文件
   - 通过Telegram Bot实时反馈系统状态
   - 支持远程查询仓位、余额、错误等信息

### 代码质量检查
```bash
# 运行类型检查
mypy .

# 运行代码格式检查
ruff check .

# 修复代码格式问题
ruff check --fix .
```

### 调试命令
```bash
# 获取账户信息
python get_info.py

# 查看日志
tail -f log/api_monitor_class_version.log
tail -f log/api_monitor_class_version_log.log
```

## 架构说明

### 核心组件

1. **Binance_api.py**: 币安API封装类
   - 处理所有交易操作（开仓、平仓、取消订单）
   - 管理仓位信息和账户余额
   - 实现订单验证和错误处理

2. **api_monitor_class_version.py**: WebSocket实时监控
   - 监听账户事件（订单状态、仓位变化）
   - 处理跟单逻辑（主账户 → 跟随账户）
   - 管理WebSocket连接和重连机制

3. **配置系统**:
   - `config.py`: 使用pydantic管理API密钥和基础配置
   - `config.yaml`: 存储账户资产和杠杆倍数配置

### 关键业务逻辑

1. **跟单机制**: 
   - MDS账户为主账户，ZACH账户为跟随账户
   - 根据`config.yaml`中的比例自动计算跟单数量
   - 支持限价单和市价单两种模式

2. **订单管理**:
   - 通过`closeOrdersID.json`追踪待平仓订单
   - 实现订单取消和重试机制
   - 处理部分成交和完全成交

3. **错误处理**:
   - WebSocket断线自动重连
   - API错误分类处理（余额不足、精度错误等）
   - Telegram通知关键事件

## 开发注意事项

1. **敏感信息**: API密钥通过`config.py`管理，不要硬编码
2. **日志文件**: 所有日志输出到`log/`目录
3. **WebSocket**: 使用Timer定期更新listen_key（每30分钟）
4. **精度处理**: 使用`symbols_info`管理不同交易对的价格和数量精度
5. **并发安全**: 使用Lock保护关键操作（如restart）