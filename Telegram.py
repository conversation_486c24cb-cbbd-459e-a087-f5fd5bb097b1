import logging
from telegram import Update, KeyboardButton, ReplyKeyboardMarkup
from telegram.ext import (
    filters,
    MessageHandler,
    ApplicationBuilder,
    CommandHandler,
    ContextTypes,
)
from telegram import error
from telegram.request import HTTPXRequest
from telegram.constants import MessageLimit
from get_info import positions, balances
import func_timeout
import traceback
import html

from config import settings

logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.ERROR,
    filename="log/Telegram.log",
    filemode="a+",
)
logger = logging.getLogger(__name__)

bot_token = settings.telegram.main_bot

run_log_path = "./log/api_monitor_class_version.log"
log_log_path = "./log/api_monitor_class_version_log.log"
tele_log_path = "./log/Telegram.log"


# Constants
DEFAULT_LOG_LINES = 10


def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass
    try:
        import unicodedata

        unicodedata.numeric(s)
        return True
    except (TypeError, ValueError):
        pass
    return False


def trim_msg(msg):
    if len(msg) <= MessageLimit.MAX_TEXT_LENGTH:
        return msg
    else:
        msg = "\n".join(msg[-MessageLimit.MAX_TEXT_LENGTH :].split("\n")[1:])
        return msg


# Generic log handler
async def handle_log_command(
    update: Update, context: ContextTypes.DEFAULT_TYPE, log_path: str
):
    """Generic handler for log commands"""
    if not update.effective_chat:
        return

    with open(log_path, "r") as f:
        log_lines = f.readlines()

    if context.args and len(context.args) > 0:
        for arg in context.args:
            if is_number(arg):
                log_lines = log_lines[-int(arg) :]
            else:
                filter_lines = []
                for line in log_lines:
                    if arg in line:
                        filter_lines.append(line)
                log_lines = filter_lines

        if len(log_lines) > 0:
            await context.bot.send_message(
                chat_id=update.effective_chat.id, text=trim_msg("\n".join(log_lines))
            )
        else:
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=(
                    f"There are no results after filtering by parameter "
                    f"{context.args}"
                ),
            )
    else:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=trim_msg("\n".join(log_lines[-DEFAULT_LOG_LINES:])),
        )


# Generic error handler
async def handle_errors_command(
    update: Update, context: ContextTypes.DEFAULT_TYPE, log_path: str
):
    """Generic handler for error commands"""
    if not update.effective_chat:
        return

    with open(log_path, "r") as f:
        log_lines = f.readlines()

    error_lines = []
    for line in log_lines:
        if " ERROR " in line:
            error_lines.append(line)

    if len(error_lines) > 0:
        if context.args and len(context.args) > 0 and is_number(context.args[0]):
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=(
                    trim_msg("\n".join(error_lines[-int(context.args[0]) :]))
                    if context.args
                    else trim_msg("\n".join(error_lines))
                ),
            )
        else:
            await context.bot.send_message(
                chat_id=update.effective_chat.id, text=trim_msg("\n".join(error_lines))
            )
    else:
        await context.bot.send_message(
            chat_id=update.effective_chat.id, text="no errors"
        )


def create_custom_keyboard():
    keyboard = [
        [
            KeyboardButton("/position"),
            KeyboardButton("/balance"),
            KeyboardButton("/errors_run"),
        ],
        [
            KeyboardButton("/unorder"),
            KeyboardButton("/proportion"),
            KeyboardButton("/log_run"),
        ],
        [
            KeyboardButton("/errors_log"),
            KeyboardButton("/log_log"),
            KeyboardButton("/log_tele"),
        ],
    ]
    return ReplyKeyboardMarkup(
        keyboard, resize_keyboard=True, one_time_keyboard=False, is_persistent=True
    )


async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    custom_keyboard_markup = create_custom_keyboard()
    if update.effective_chat:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="系统监控bot启动",
            reply_markup=custom_keyboard_markup,
        )


async def log_run(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await handle_log_command(update, context, run_log_path)


async def log_log(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await handle_log_command(update, context, log_log_path)


async def log_tele(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await handle_log_command(update, context, tele_log_path)


async def errors_run(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await handle_errors_command(update, context, run_log_path)


async def errors_log(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await handle_errors_command(update, context, log_log_path)


async def unorder(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.effective_chat:
        return

    with open(run_log_path, "r") as f:
        log_lines = f.readlines()

    order_lines = []
    for line in log_lines:
        if "速度下单：" in line or "(未跟随)" in line:
            order_lines.append(line)
    if len(order_lines) > 0:
        await context.bot.send_message(
            chat_id=update.effective_chat.id, text=trim_msg("\n".join(order_lines))
        )
    else:
        await context.bot.send_message(
            chat_id=update.effective_chat.id, text="no unorders"
        )


async def proportion(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.effective_chat:
        return

    with open(run_log_path, "r") as f:
        log_lines = f.readlines()

    for line in log_lines[::-1]:
        if "update proportion: " in line:
            current_proportion = line.split("update proportion: ")[1]
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"proportion: {current_proportion}",
            )
            break

    for line in log_lines[::-1]:
        if "update MARKET / LIMIT: " in line:
            current_market_limit = line.split("update MARKET / LIMIT: ")[1]
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"MARKET/LIMIT: {current_market_limit}",
            )
            break


async def position(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.effective_chat:
        return

    if context.args and len(context.args) > 0:
        for arg in context.args:
            await context.bot.send_message(
                chat_id=update.effective_chat.id, text="\n".join(positions(arg))
            )
    else:
        try:
            mds_position_msgs = positions("mds")
            zach_position_msgs = positions("zach")
            msg = "\n".join(mds_position_msgs + zach_position_msgs)
        except func_timeout.exceptions.FunctionTimedOut:
            msg = "超时"
        await context.bot.send_message(chat_id=update.effective_chat.id, text=msg)


async def balance(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.effective_chat:
        return

    try:
        mds_asset = balances("mds")
        zach_asset = balances("zach")
        msg = f"====== mds ======\n{mds_asset}\n====== zach ======\n{zach_asset}"
    except func_timeout.exceptions.FunctionTimedOut:
        msg = "超时"
    await context.bot.send_message(chat_id=update.effective_chat.id, text=msg)


async def unknown(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_chat:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="Sorry, I didn't understand that command.",
        )


async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:

    if not context.error:
        return

    tb_list = traceback.format_exception(
        None, context.error, context.error.__traceback__, limit=None
    )
    tb_string = "".join(tb_list[-2:])
    print_tb_string = "\n".join(tb_list[-2:])
    message = (
        "An exception was raised while handling an update\n"
        f"<pre>{html.escape(tb_string)}</pre>"
    )

    if isinstance(context.error, error.NetworkError):
        network_error = "Telegram network error."
        logger.critical(network_error)
    else:
        logger.error(print_tb_string)
        if hasattr(update, "effective_chat") and update.effective_chat:
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=message,
                parse_mode="HTML",
            )


if __name__ == "__main__":
    # application = ApplicationBuilder().token(bot_token).build()
    trequest = HTTPXRequest(
        connection_pool_size=20,
        read_timeout=7,
    )
    application = (
        ApplicationBuilder()
        .get_updates_read_timeout(42)
        .token(bot_token)
        .request(trequest)
        .build()
    )

    application.add_error_handler(error_handler)

    start_handler = CommandHandler("start", start)
    application.add_handler(start_handler)

    log_run_handler = CommandHandler("log_run", log_run)
    application.add_handler(log_run_handler)

    log_log_handler = CommandHandler("log_log", log_log)
    application.add_handler(log_log_handler)

    log_tele_handler = CommandHandler("log_tele", log_tele)
    application.add_handler(log_tele_handler)

    run_error_handler = CommandHandler("errors_run", errors_run)
    application.add_handler(run_error_handler)

    log_error_handler = CommandHandler("errors_log", errors_log)
    application.add_handler(log_error_handler)

    position_handler = CommandHandler("position", position)
    application.add_handler(position_handler)

    unorder_handler = CommandHandler("unorder", unorder)
    application.add_handler(unorder_handler)

    proportion_handler = CommandHandler("proportion", proportion)
    application.add_handler(proportion_handler)

    balance_handler = CommandHandler("balance", balance)
    application.add_handler(balance_handler)

    unknown_handler = MessageHandler(filters.COMMAND, unknown)
    application.add_handler(unknown_handler)

    try:
        application.run_polling(
            bootstrap_retries=10,
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True,
        )
    except Exception as err:
        print(err)
