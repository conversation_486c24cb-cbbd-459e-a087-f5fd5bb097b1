import json
import logging

from util import Telegram
from config import settings
from threading import Timer, Lock
import time

from binance.websocket.um_futures.websocket_client import UMFuturesWebsocketClient
from Binance_api_log import Binance_log

import yaml


# 时间常量（秒）
LISTEN_KEY_RENEW_INTERVAL = 45 * 60  # 45分钟
WEBSOCKET_REFRESH_INTERVAL = 23 * 60 * 60  # 23小时
POSITION_UPDATE_INTERVAL = 30 * 60  # 30分钟
RETRY_DELAY = 5  # 5秒
SHORT_DELAY = 1  # 1秒
MEDIUM_DELAY = 8  # 8秒
SHUTDOWN_DELAY = 2  # 2秒
ERROR_RETRY_DELAY = 60  # 1分钟

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(levelname)s %(filename)s:%(funcName)s:%(lineno)d  %(message)s",  # 定义输出log的格式
    datefmt="%Y-%m-%d %A %H:%M:%S",
    filename="log/api_monitor_class_version_log.log",
    filemode="a+",
)
logger = logging.getLogger()


# 环境变量
bot_token = settings.telegram.monitor_bot


class Monitor:

    def __init__(self) -> None:
        self.listen_key = (
            "3JpWLKX0vAy64GoLd6aTlJSerueLTGSTRbx8EuG7kk5g2ylNKaXp3bnnpHEPAJPO"
        )
        logger.info(f"MDS Log ListenKey is inited: {self.listen_key}")
        self.zach_binance_log_client = Binance_log(logger)
        logger.info("Binance Log client inited.")
        self.telegram = Telegram(bot_token, logger)
        logger.info("Log telegram inited.")

        self.account = json.load(open("mds_account.json", "r"))
        self.positions = self.account["positions"]
        logger.info(f"init MDS positions: {self.positions}")
        self.asset = self.account["asset"]

        # 添加状态锁
        self.restart_lock = Lock()
        self.is_restarting = False

        Timer(LISTEN_KEY_RENEW_INTERVAL, self.update_listen_key).start()
        self.update_proportion_accountInfo(True)

    def start(self):
        self.ws_client = None
        self.timer = None
        self.create_websocket()

    def create_websocket(self):
        try:
            self.ws_client = UMFuturesWebsocketClient(
                on_message=self.handle_message,
                on_close=self.handle_close,
                on_error=self.handle_error,
            )
            logger.info("create new websocketclient.")
            self.monitor()
        except Exception as e:
            logger.error(e, exc_info=True)
        finally:
            if self.timer and self.timer.is_alive():
                self.timer.cancel()
                logger.info("cancel old Timer.")
            self.timer = Timer(WEBSOCKET_REFRESH_INTERVAL, self.just_close)
            self.timer.start()
            logger.info("new Timer: update websocket in 23h ...")

    def monitor(self):
        try:
            self.ws_client.user_data(listen_key=self.listen_key)
            msg = "listen in a new websocketclient"
            logger.info(msg)
        except Exception as e:
            logger.error(e, exc_info=True)
            self.restart()

    def restart(self):
        # 使用锁避免并发重启
        if not self.restart_lock.acquire(blocking=False):
            logger.info("重启已在进行中，忽略此次请求")
            return

        try:
            # 检查重启状态
            if self.is_restarting:
                logger.info("重启已在进行中，忽略此次请求")
                return

            self.is_restarting = True

            # 关闭旧的websocket
            if self.ws_client:
                try:
                    self.ws_client.stop()
                    time.sleep(SHUTDOWN_DELAY)  # 等待充分关闭
                except Exception as e:
                    logger.error(f"close old websocket: {e}")

            self.ws_client = None

            # 创建新的websocket
            try:
                self.create_websocket()
                msg = "restarted websocketclient and listening"
                logger.info(msg)
            except Exception as e:
                logger.error(e, exc_info=True)
                # 使用定时器延迟重试，避免递归调用
                Timer(RETRY_DELAY, self.restart).start()
        finally:
            self.is_restarting = False
            self.restart_lock.release()

    def just_close(self):
        # 只关闭websocket，重启交给handle_close避免重复监听
        if self.ws_client:
            try:
                self.ws_client.stop()
            except Exception as e:
                logger.error(f"close old websocket: {e}")

    def handle_close(self, _):
        msg = "handle close ..."
        logger.info(msg)
        # 检查是否已经在重启中
        if not self.is_restarting:
            # 使用定时器延迟重启
            Timer(SHORT_DELAY, self.restart).start()
        else:
            logger.info("已在重启过程中，忽略此次关闭事件")

    def handle_error(self, _, error):
        logger.error(error)
        # 检查是否已经在重启中
        if not self.is_restarting:
            # 使用定时器延迟重启
            Timer(SHORT_DELAY, self.restart).start()
        else:
            logger.info("已在重启过程中，忽略此次错误事件")

    def update_listen_key(self):
        try:
            # self.client.renew_listen_key(self.listen_key)
            logger.info("ListenKey is renewed.")
            Timer(LISTEN_KEY_RENEW_INTERVAL, self.update_listen_key).start()
        except Exception as e:
            logger.error(e, exc_info=True)
            Timer(ERROR_RETRY_DELAY, self.update_listen_key).start()

    def cancel_order_helper(self, event):
        side = event["o"]["S"]  # 订单方向
        z = float(event["o"]["z"])  # 订单累计已成交量
        positionSide = event["o"]["ps"]  # 持仓方向

        if z > 0:
            try:
                if side == "BUY":
                    if positionSide == "SHORT":
                        self.update_proportion_accountInfo()
                        Timer(
                            MEDIUM_DELAY, self.update_proportion_accountInfo, (False,)
                        ).start()
                elif side == "SELL":
                    if positionSide == "LONG":
                        self.update_proportion_accountInfo()
                        Timer(
                            MEDIUM_DELAY, self.update_proportion_accountInfo, (False,)
                        ).start()
            except Exception as e:
                logger.error(e, exc_info=True)

    def filled_order_trade_helper(self, event):
        side = event["o"]["S"]  # 订单方向
        positionSide = event["o"]["ps"]  # 持仓方向

        try:
            if side == "BUY":
                if positionSide == "SHORT":
                    self.update_proportion_accountInfo()
                    Timer(
                        MEDIUM_DELAY, self.update_proportion_accountInfo, (False,)
                    ).start()

            elif side == "SELL":
                if positionSide == "LONG":
                    self.update_proportion_accountInfo()
                    Timer(
                        MEDIUM_DELAY, self.update_proportion_accountInfo, (False,)
                    ).start()

        except Exception as e:
            logger.error(e, exc_info=True)

    def dump_account(self):
        self.account["positions"] = self.positions
        self.account["asset"] = self.asset
        json.dump(self.account, open("mds_account.json", "w"))

    def update_position_helper(self, event):
        if "a" in event:
            if "m" in event["a"]:
                if event["a"]["m"] == "ORDER":
                    if "P" in event["a"]:
                        for position in event["a"]["P"]:
                            if float(position["pa"]) != 0:
                                if position["s"] not in self.positions:
                                    self.positions[position["s"]] = {}
                                self.positions[position["s"]][position["ps"]] = {
                                    "pa": abs(float(position["pa"])),
                                    "ep": float(position["ep"]),
                                }
                            else:
                                if (
                                    position["s"] in self.positions
                                    and position["ps"] in self.positions[position["s"]]
                                ):
                                    self.positions[position["s"]].pop(
                                        position["ps"], None
                                    )
                                    if len(self.positions[position["s"]]) == 0:
                                        self.positions.pop(position["s"], None)
                    if "B" in event["a"]:
                        for asset in event["a"]["B"]:
                            if asset["a"] == "USDT":
                                self.asset = float(asset["wb"])
                                logger.info(f"MDS asset: {self.asset}")
                elif event["a"]["m"] == "FUNDING_FEE":
                    if "B" in event["a"]:
                        for asset in event["a"]["B"]:
                            if asset["a"] == "USDT":
                                self.asset = float(asset["wb"])
                                logger.info(f"MDS asset: {self.asset}")
        self.dump_account()

    def handle_message(self, _, message):
        event = json.loads(message)
        if "e" in event:
            if event["e"] == "ORDER_TRADE_UPDATE":
                # 订单交易更新推送
                if "o" in event:
                    x = event["o"]["x"]  # 本次事件的具体执行类型
                    X = event["o"]["X"]  # 订单的当前状态
                    if X == "FILLED" and x == "TRADE":  # 订单成交才操作
                        self.filled_order_trade_helper(event)
                    elif X == "CANCELED":  # cancel
                        self.cancel_order_helper(event)
            elif event["e"] == "ACCOUNT_UPDATE":
                # Balance 和 Position 更新推送
                self.update_position_helper(event)
            elif event["e"] == "listenKeyExpired":
                # listenKey过期推送 - 需要从配置或API获取新的listen_key
                logger.warning(
                    "[listenKeyExpired] Listen key expired, need to renew from API"
                )
        logger.info(event)

    def close_remain_position_helper(self, symbol, positionSide):
        if (
            symbol in self.zach_binance_log_client.positions
            and positionSide in self.zach_binance_log_client.positions[symbol]
        ):
            try:
                if positionSide == "SHORT":
                    self.zach_binance_log_client.close_remain_position(
                        symbol, "BUY", positionSide
                    )
                elif positionSide == "LONG":
                    self.zach_binance_log_client.close_remain_position(
                        symbol, "SELL", positionSide
                    )
            except Exception as e:
                logger.error(e, exc_info=True)

    def handle_position_error(self):
        current_positions = self.zach_binance_log_client.positions

        for symbol in current_positions:
            if symbol not in self.positions:
                for positionSide in current_positions[symbol]:
                    self.close_remain_position_helper(symbol, positionSide)
            else:
                for positionSide in current_positions[symbol]:
                    if positionSide not in self.positions[symbol]:
                        self.close_remain_position_helper(symbol, positionSide)

    def update_proportion_accountInfo(self, is_timer=False):
        self.zach_binance_log_client.update_accountInfo()
        self.dump_account()

        if len(self.zach_binance_log_client.positions) == len(self.positions):
            if len(self.positions) > 0:
                if (
                    self.zach_binance_log_client.positions.keys()
                    == self.positions.keys()
                ):
                    for symbol in self.positions:
                        if (
                            self.zach_binance_log_client.positions[symbol].keys()
                            != self.positions[symbol].keys()
                        ):
                            msg = "position error!"
                            logger.error(msg)
                            self.telegram.send_message(msg)
                            self.handle_position_error()
                            Timer(
                                SHORT_DELAY,
                                self.update_proportion_accountInfo,
                                (False,),
                            ).start()
                else:
                    msg = "position error!"
                    logger.error(msg)
                    self.telegram.send_message(msg)
                    self.handle_position_error()
                    Timer(
                        SHORT_DELAY, self.update_proportion_accountInfo, (False,)
                    ).start()

            elif len(self.zach_binance_log_client.positions) == 0:

                with open(file="config.yaml", mode="r", encoding="utf-8") as f:
                    res = yaml.safe_load(f)

                res["zach_asset"] = self.zach_binance_log_client.asset
                logger.info(f"update zach asset: {self.zach_binance_log_client.asset}")
                res["mds_asset"] = self.asset
                logger.info(f"update mds asset: {self.asset}")

                with open(file="config.yaml", mode="w", encoding="utf-8") as f:
                    yaml.dump(res, f)
                logger.info("update asset in config")
        else:
            msg = "position error!"
            logger.error(msg)
            self.telegram.send_message(msg)
            self.handle_position_error()
            Timer(SHORT_DELAY, self.update_proportion_accountInfo, (False,)).start()

        if is_timer:
            logger.info("update proportion and accountInfo in 30m ...")
            Timer(
                POSITION_UPDATE_INTERVAL, self.update_proportion_accountInfo, (True,)
            ).start()


def main():
    monitor = Monitor()
    monitor.start()


if __name__ == "__main__":
    main()
