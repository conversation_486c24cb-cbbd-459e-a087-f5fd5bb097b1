{"permissions": {"allow": ["<PERSON><PERSON>(mypy:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__morphllm-fast-apply__edit_file", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(python:*)", "mcp__serena__activate_project", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__read_memory", "mcp__serena__write_memory", "mcp__serena__search_for_pattern", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__serena__think_about_collected_information", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(grep:*)", "Bash(ruff check:*)", "mcp__serena__find_referencing_symbols"], "deny": [], "ask": []}}