import json
import datetime
import requests

CHAT_ID = "5523514062"


def pretty_print(report):
    breport = json.dumps(report, indent=4, sort_keys=True, ensure_ascii=False)
    return breport


class Telegram:

    def __init__(self, bot_token, logger) -> None:
        self.bot_token = bot_token
        self.logger = logger

    def send_message(self, msg: str, informTime=None):
        if informTime:
            message = (
                f"{msg}\n{informTime}\t"
                f'{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
            )
        else:
            message = f'{msg}\n{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        try:

            requests.post(
                f"https://api.telegram.org/bot{self.bot_token}/sendMessage",
                json={"chat_id": CHAT_ID, "text": message},
            )
        except Exception as e:
            if msg != "Telegram出错尽快检查！":
                self.logger.error(e)
                self.send_message("Telegram出错尽快检查！")


def telegram(bot_token, content, informTime=None):
    if informTime:
        message = (
            f"{content}\n{informTime}\t"
            f'{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        )
    else:
        message = f'{content}\n{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
    try:
        url_telegram = f"https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={CHAT_ID}&text={message}"
        requests.get(url_telegram)
    except Exception as e:
        print(e)
