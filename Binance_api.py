from binance.um_futures import UMFutures
from binance.error import ClientError
from binance.helpers import round_step_size
from util import pretty_print, Telegram
from config import settings
import yaml

bot_token = settings.telegram.binance_bot
# logger = logging.getLogger(__name__)


class Binance:

    def __init__(self, logger=None) -> None:
        # 测试网
        self.um_futures_client = UMFutures(
            key=settings.binance.zach_account.api_key,
            secret=settings.binance.zach_account.api_secret,
        )

        self.logger = logger
        self.telegram = Telegram(bot_token, logger)
        self.logger.info("Binance Telegram inited.")
        self.telegram.send_message("Binance Telegram inited.")
        self.update_proportion()
        # 未成交挂单的加权价格与数量，按交易对与持仓方向细分
        self.unOrderPrice: dict[str, dict[str, float]] = {}
        self.unOrderAmount: dict[str, dict[str, float]] = {}
        self.unLimitOrderPri = 0
        self.unLimitOrderQty = 0
        self.update_accountInfo()
        self.update_symbols_info()

    def update_proportion(self):
        with open(file="config.yaml", mode="r", encoding="utf-8") as f:
            res = yaml.safe_load(f)
        asset_proportion = res["zach_asset"] / res["mds_asset"] / 2
        margin_proportion = res["zach_margin"] / res["mds_margin"]
        self.proportion = int(asset_proportion * margin_proportion * 10000) / 10000.0
        self.logger.info(f"update proportion: {self.proportion}")

    def clear_unorderamount(self):
        for symbol in self.unOrderAmount:
            if symbol not in self.positions:
                for positionSide in self.unOrderAmount[symbol]:
                    self.unOrderPrice[symbol][positionSide] = 0.0
                    self.unOrderAmount[symbol][positionSide] = 0.0
            else:
                for positionSide in self.unOrderAmount[symbol]:
                    if positionSide not in self.positions[symbol]:
                        self.unOrderPrice[symbol][positionSide] = 0.0
                        self.unOrderAmount[symbol][positionSide] = 0.0

    def update_accountInfo(self, update_unorder=False):
        try:
            response = self.um_futures_client.account(recvWindow=6000)
            if "positions" in response:
                positions = {}
                for position in response["positions"]:
                    if float(position["positionAmt"]) != 0:
                        if position["symbol"] not in positions:
                            positions[position["symbol"]] = {}
                        # if position["positionSide"] not in positions[
                        #         position["symbol"]]:
                        positions[position["symbol"]][position["positionSide"]] = abs(
                            float(position["positionAmt"])
                        )
                self.positions = positions
                self.logger.info(f"ZACH positions: {self.positions}")
        except ClientError as error:
            self.logger.error(
                "Found error. status: {}, error code: {}, error message: {}".format(
                    error.status_code, error.error_code, error.error_message
                )
            )
        except Exception as e:
            self.telegram.send_message(e)
            # telegram(bot_token, e)
            self.logger.error(e, exc_info=True)
        if update_unorder:
            self.clear_unorderamount()

    def get_position_quantity(self, symbol, positionSide):
        try:
            response = self.um_futures_client.account(recvWindow=6000)
            if "positions" in response:
                for position in response["positions"]:
                    if (
                        position["symbol"] == symbol
                        and position["positionSide"] == positionSide
                    ):
                        return abs(float(position["positionAmt"]))
                return 0
        except ClientError as error:
            self.logger.error(
                "Found error. status: {}, error code: {}, error message: {}".format(
                    error.status_code, error.error_code, error.error_message
                )
            )
        except Exception as e:
            self.telegram.send_message(e)
            # telegram(bot_token, e)
            self.logger.error(e, exc_info=True)
        return -1

    def update_symbols_info(self):
        try:
            info = self.um_futures_client.exchange_info()
            symbols_info = {}
            for item in info["symbols"]:
                symbols_info[item["symbol"]] = {
                    "quantityPrecision": item["quantityPrecision"],
                    "pricePrecision": item["pricePrecision"],
                }
                for f in item["filters"]:
                    if f["filterType"] == "PRICE_FILTER":
                        symbols_info[item["symbol"]]["tickSize"] = float(f["tickSize"])
                    elif f["filterType"] == "LOT_SIZE":
                        symbols_info[item["symbol"]]["minQty"] = float(f["minQty"])
                        symbols_info[item["symbol"]]["stepSize"] = float(f["stepSize"])
            with open("symbols_info.json", "w") as f:
                f.write(pretty_print(symbols_info))
            self.symbols_info = symbols_info
            self.logger.info("update symbols info")
        except Exception as e:
            self.logger.error(e, exc_info=True)

    def get_valid_price(self, price, symbol):
        return round_step_size(price, self.symbols_info[symbol]["tickSize"])

    def get_valid_amount(self, quantity, symbol):
        return round_step_size(quantity, self.symbols_info[symbol]["stepSize"])

    def new_close_order(
        self, symbol, side, type, positionSide, amount, price, stopPrice
    ):
        price = self.get_valid_price(price, symbol)
        quantity = self.get_valid_amount(amount * self.proportion, symbol)

        # 挂单数量不能超过持仓
        if symbol in self.positions and positionSide in self.positions[symbol]:
            if quantity > self.positions[symbol][positionSide]:
                self.update_accountInfo()
                if quantity > self.positions[symbol][positionSide]:
                    quantity = self.positions[symbol][positionSide]
        else:
            self.update_accountInfo()
            if symbol in self.positions and positionSide in self.positions[symbol]:
                if quantity > self.positions[symbol][positionSide]:
                    quantity = self.positions[symbol][positionSide]

        if quantity > 0:
            try:
                newClientOrderId = f"{symbol}-{side}-{quantity}-{price}-{type}"[:32]
                if type == "LIMIT":
                    response = self.um_futures_client.new_order(
                        symbol=symbol,
                        side=side,
                        type=type,
                        positionSide=positionSide,
                        timeInForce="GTC",
                        quantity=quantity,
                        price=price,
                        newClientOrderId=newClientOrderId,
                    )
                elif type == "MARKET":
                    response = self.um_futures_client.new_order(
                        symbol=symbol,
                        side=side,
                        type=type,
                        positionSide=positionSide,
                        quantity=quantity,
                        newClientOrderId=newClientOrderId,
                    )
                elif type == "TAKE_PROFIT" or type == "STOP":
                    newClientOrderId = (
                        f"{symbol}-{side}-{quantity}-{price}-{stopPrice}-{type}"[:32]
                    )
                    response = self.um_futures_client.new_order(
                        symbol=symbol,
                        side=side,
                        type=type,
                        positionSide=positionSide,
                        timeInForce="GTC",
                        quantity=quantity,
                        price=price,
                        newClientOrderId=newClientOrderId,
                        stopPrice=stopPrice,
                    )
                elif type == "TAKE_PROFIT_MARKET" or type == "STOP_MARKET":
                    newClientOrderId = f"{symbol}-{side}-{quantity}-{stopPrice}-{type}"[
                        :32
                    ]
                    response = self.um_futures_client.new_order(
                        symbol=symbol,
                        side=side,
                        type=type,
                        positionSide=positionSide,
                        timeInForce="GTC",
                        quantity=quantity,
                        newClientOrderId=newClientOrderId,
                        stopPrice=stopPrice,
                    )
                else:
                    self.logger.error(f"NEW ORDER TYPE: {type}")
                if "executedQty" in response:
                    if type == "TAKE_PROFIT":
                        msg = f"[止盈] [{stopPrice}:{price}]【{side}】止盈 【{symbol}】【{quantity}个】"
                    elif type == "STOP":
                        msg = f"[止损] [{stopPrice}:{price}]【{side}】止损 【{symbol}】【{quantity}个】"
                    elif type == "TAKE_PROFIT_MARKET":
                        msg = f"[市价止盈] [{stopPrice}]【{side}】市价止盈 【{symbol}】【{quantity}个】"
                    elif type == "STOP_MARKET":
                        msg = f"[市价止损] [{stopPrice}]【{side}】市价止损 【{symbol}】【{quantity}个】"
                    else:
                        msg = f"[平仓 {type}] [{price}]【{side}】平仓 【{symbol}】【{quantity}个】"
                    self.telegram.send_message(msg)
                    # telegram(bot_token, msg)
                    self.logger.info(msg)
                    return newClientOrderId
                else:
                    self.logger.error(response)
                    self.telegram.send_message(response)
                    # telegram(bot_token, response)

            except ClientError as error:
                self.logger.error(
                    "Found error. status: {}, error code: {}, error message: {}".format(
                        error.status_code, error.error_code, error.error_message
                    )
                )
                self.telegram.send_message(error.error_message)
                # telegram(bot_token, error.error_message)
            except Exception as e:
                msg = f"[平仓错误请检查原因] [{type}] [{price}]【{side}】平仓 【{symbol}】【{quantity}个"
                self.telegram.send_message(msg)
                self.telegram.send_message(e)
                # telegram(bot_token, msg)
                # telegram(bot_token, e)
                self.logger.error(e, exc_info=True)

    def open_position(self, symbol, side, positionSide, amount, price):
        # Ensure the symbol is initialized in unOrderAmount and unOrderPrice dictionaries
        self.unOrderPrice.setdefault(symbol, {})
        self.unOrderAmount.setdefault(symbol, {})

        # Determine if there is an existing order for the given symbol and position side
        existing_order = self.unOrderAmount[symbol].get(positionSide, 0) > 0

        if existing_order:
            # Update average price and total amount for the position side
            prev_amount = self.unOrderAmount[symbol][positionSide]
            prev_price = self.unOrderPrice[symbol][positionSide]
            new_amount = prev_amount + amount
            self.unOrderPrice[symbol][positionSide] = (
                prev_amount * prev_price + amount * price
            ) / new_amount
            self.unOrderAmount[symbol][positionSide] = new_amount

            # Get valid price and amount for placing an order
            price = self.get_valid_price(
                self.unOrderPrice[symbol][positionSide], symbol
            )
            quantity = self.get_valid_amount(new_amount * self.proportion, symbol)

            if quantity >= self.symbols_info[symbol]["minQty"]:
                self._place_limit_order(symbol, side, positionSide, price, quantity)
        else:
            # Get valid amount for placing a market order
            quantity = self.get_valid_amount(amount * self.proportion, symbol)

            if quantity >= self.symbols_info[symbol]["minQty"]:
                self._place_market_order(
                    symbol, side, positionSide, price, amount, quantity
                )
            else:
                # Log insufficient quantity to place an order
                self.unOrderPrice[symbol][positionSide] = price
                self.unOrderAmount[symbol][positionSide] = amount
                self.logger.info(
                    f"[市价] {symbol} {positionSide} 数量不够无法下单: "
                    f"unOrderAmount: {self.unOrderAmount[symbol][positionSide]} "
                    f"unOrderPrice: {self.unOrderPrice[symbol][positionSide]}"
                )

    def _place_limit_order(self, symbol, side, positionSide, price, quantity):
        try:
            response = self.um_futures_client.new_order(
                symbol=symbol,
                side=side,
                type="LIMIT",
                positionSide=positionSide,
                quantity=quantity,
                timeInForce="GTC",
                price=price,
            )
            self._handle_order_response(
                response, symbol, side, positionSide, price, quantity, "限价"
            )
        except ClientError as error:
            self._handle_client_error(
                error, symbol, side, positionSide, price, quantity, "限价"
            )
        except Exception as e:
            self._handle_general_exception(
                e, symbol, side, positionSide, price, quantity, "限价"
            )

    def _place_market_order(self, symbol, side, positionSide, price, amount, quantity):
        try:
            response = self.um_futures_client.new_order(
                symbol=symbol,
                side=side,
                type="MARKET",
                positionSide=positionSide,
                quantity=quantity,
            )
            self._handle_order_response(
                response, symbol, side, positionSide, price, quantity, "市价", amount
            )
        except ClientError as error:
            self._handle_client_error(
                error, symbol, side, positionSide, price, quantity, "市价", amount
            )
        except Exception as e:
            self._handle_general_exception(
                e, symbol, side, positionSide, price, quantity, "市价", amount
            )

    def _handle_order_response(
        self,
        response,
        symbol,
        side,
        positionSide,
        price,
        quantity,
        order_type,
        amount=None,
    ):
        if response and isinstance(response, dict) and "executedQty" in response:
            msg = (
                f"[{order_type}]【{price}】【{side}】挂单 {symbol} "
                f"【{quantity}个，成交【{response['executedQty']}个】"
            )
            self.telegram.send_message(msg)
            self.unOrderPrice[symbol][positionSide] = 0.0
            self.unOrderAmount[symbol][positionSide] = 0.0
            # self.unOrderPrice[symbol][positionSide] = price
            # self.unOrderAmount[symbol][positionSide] = (
            #     amount or self.unOrderAmount[symbol][positionSide]
            # ) - quantity / self.proportion
            self.logger.info(response)
        else:
            self.telegram.send_message(response)
            self.logger.error(response)
            if amount:
                self.unOrderPrice[symbol][positionSide] = price
                self.unOrderAmount[symbol][positionSide] = amount

    def _handle_client_error(
        self,
        error,
        symbol,
        side,
        positionSide,
        price,
        quantity,
        order_type,
        amount=None,
    ):
        if "Margin is insufficient" in error.error_message:
            msg = (
                f"速度下单：[ {order_type} ]【{price}】【{side}】挂单 {symbol} "
                f"【{quantity}个\n原因：{error.error_message}"
            )
            self.telegram.send_message(msg)
            self.logger.error(msg)
            self.unOrderPrice[symbol][positionSide] = price
            self.unOrderAmount[symbol][positionSide] = (
                amount or self.unOrderAmount[symbol][positionSide]
            )
            if amount:
                quantity = self.get_valid_amount(quantity * 0.99, symbol)
                if quantity >= self.symbols_info[symbol]["minQty"]:
                    self._place_market_order(
                        symbol, side, positionSide, price, amount * 0.99, quantity
                    )
        elif "Order's notional must be no smaller than" in error.error_message:
            msg = f"{error.error_message}: 【{price}】【{side}】【{symbol}】【{quantity}个"
            self.logger.error(msg)
            self.unOrderPrice[symbol][positionSide] = price
            self.unOrderAmount[symbol][positionSide] = (
                amount or self.unOrderAmount[symbol][positionSide]
            )
        else:
            msg = f"NEW ERROR: {error.error_message}:【{price}】【{side}】【{symbol}】【{quantity}个"
            self.telegram.send_message(msg)
            self.logger.error(msg)
            self.unOrderPrice[symbol][positionSide] = price
            self.unOrderAmount[symbol][positionSide] = (
                amount or self.unOrderAmount[symbol][positionSide]
            )

    def _handle_general_exception(
        self, e, symbol, side, positionSide, price, quantity, order_type, amount=None
    ):
        msg = f"下单失败请检查原因: [ {order_type} ]【{price}】【{side}】挂单 {symbol} 【{quantity}个"
        self.telegram.send_message(msg)
        self.telegram.send_message(e)
        self.logger.error(e, exc_info=True)
        self.unOrderPrice[symbol][positionSide] = price
        self.unOrderAmount[symbol][positionSide] = (
            amount or self.unOrderAmount[symbol][positionSide]
        )

    def parse_position_event(self, event, symbol, positionSide):
        if "a" in event:
            if "m" in event["a"]:
                if event["a"]["m"] == "ORDER":
                    if "P" in event["a"]:
                        for position in event["a"]["P"]:
                            if (
                                position["s"] == symbol
                                and position["ps"] == positionSide
                            ):
                                return abs(float(position["pa"]))
        return -1

    def close_position(
        self, symbol, side, positionSide, amount, position_event, is_close_remain=False
    ):
        quantity = self.get_valid_amount(amount * self.proportion, symbol)

        current_quantity = self.get_position_quantity(symbol, positionSide)
        if current_quantity < 0:
            self.update_accountInfo()
            if symbol in self.positions and positionSide in self.positions[symbol]:
                current_quantity = self.positions[symbol][positionSide]
        mds_quantity = self.parse_position_event(position_event, symbol, positionSide)
        if mds_quantity >= 0:
            close_quantity = current_quantity - mds_quantity * self.proportion
            if close_quantity > 0:
                quantity = self.get_valid_amount(close_quantity, symbol)
            elif current_quantity >= 0:
                quantity = 0

        if quantity >= self.symbols_info[symbol]["minQty"]:
            try:
                response = self.um_futures_client.new_order(
                    symbol=symbol,
                    side=side,
                    type="MARKET",
                    quantity=quantity,
                    positionSide=positionSide,
                )
                if "executedQty" in response:
                    self.telegram.send_message(
                        f"[市价平仓]【{side}】 {symbol}，平仓【{quantity}】个，成交【{response['executedQty']}个】"
                    )
                else:
                    self.telegram.send_message(response)
                    self.logger.error(response)
            except ClientError as error:
                self.logger.error(
                    "Found error. status: {}, error code: {}, error message: {}".format(
                        error.status_code, error.error_code, error.error_message
                    )
                )
                self.telegram.send_message(error.error_message)
            except Exception as e:
                self.telegram.send_message(
                    f"平仓失败请检查原因：【{side}】 {symbol}，平仓【{quantity}】个"
                )
                # telegram(bot_token,
                #          f"平仓失败请检查原因：【{side}】 {symbol}，平仓【{quantity}】个")
                self.telegram.send_message(e)
                # telegram(bot_token, e)
                self.logger.error(e, exc_info=True)
        if is_close_remain and mds_quantity == 0:
            # self.close_remain_position(symbol, side, positionSide)
            self.cancel_orders(symbol, positionSide)
            if (
                symbol in self.unOrderAmount
                and positionSide in self.unOrderAmount[symbol]
            ):
                self.unOrderPrice[symbol][positionSide] = 0.0
                self.unOrderAmount[symbol][positionSide] = 0.0
            self.update_accountInfo(True)

    def cancel_orders(self, symbol, positionSide=None):
        if not positionSide:
            try:
                response = self.um_futures_client.cancel_open_orders(symbol)
                # print(response)
                self.logger.info(f"cancel all {symbol} orders")
                self.logger.info(response)
            except ClientError as error:
                self.logger.error(
                    "Found error. status: {}, error code: {}, error message: {}".format(
                        error.status_code, error.error_code, error.error_message
                    )
                )
                self.telegram.send_message(error.error_message)
                # telegram(bot_token, error.error_message)
            except Exception as e:
                self.telegram.send_message("订单取消失败")
                # telegram(bot_token, f'订单取消失败')
                self.telegram.send_message(e)
                # telegram(bot_token, e)
                self.logger.error(e, exc_info=True)
        else:
            try:
                cancelOrders = []
                openOrders = self.um_futures_client.get_orders(symbol=symbol)
                for order in openOrders:
                    if order["positionSide"] == positionSide:
                        cancelOrders.append(order["orderId"])
                for i in range(0, len(cancelOrders), 10):
                    response = self.um_futures_client.cancel_batch_order(
                        symbol=symbol,
                        orderIdList=cancelOrders[i : i + 10],
                        origClientOrderIdList=None,
                    )
                    self.logger.info(f"cancel all {symbol} {positionSide} orders")
                    self.logger.info(response)
            except Exception as e:
                self.logger.error(e, exc_info=True)
                self.telegram.send_message(e)
                # telegram(bot_token, e)

    def cancel_order(self, symbol, orderId=None, origClientOrderId=None):
        try:
            response = self.um_futures_client.cancel_order(
                symbol=symbol, orderId=orderId, origClientOrderId=origClientOrderId
            )
            # print(response)
            self.logger.info(f"cancel {origClientOrderId} order.")
            self.logger.info(response)
        except ClientError as error:
            self.logger.error(
                "Found error. status: {}, error code: {}, error message: {}".format(
                    error.status_code, error.error_code, error.error_message
                )
            )
            self.telegram.send_message(error.error_message)
            # telegram(bot_token, error.error_message)
        except Exception as e:
            self.telegram.send_message("订单取消失败")
            # telegram(bot_token, f'订单取消失败')
            self.telegram.send_message(e)
            # telegram(bot_token, e)
            self.logger.error(e, exc_info=True)

    def change_leverage(self, symbol, leverage):
        try:
            self.um_futures_client.change_leverage(symbol=symbol, leverage=leverage)
            msg = f"change {symbol} leverage to {leverage}."
            self.logger.info(msg)
            self.telegram.send_message(msg)
            # telegram(bot_token, msg)
        except ClientError as error:
            self.logger.error(
                "Found error. status: {}, error code: {}, error message: {}".format(
                    error.status_code, error.error_code, error.error_message
                )
            )
            self.telegram.send_message(error.error_message)
            # telegram(bot_token, error.error_message)
        except Exception as e:
            self.telegram.send_message("杠杆修改失败")
            # telegram(bot_token, f'杠杆修改失败')
            self.telegram.send_message(e)
            # telegram(bot_token, e)
            self.logger.error(e, exc_info=True)
